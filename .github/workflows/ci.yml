name: CI
on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main
      - next

jobs:
  lint:
    timeout-minutes: 10
    name: lint
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Set up Java
        uses: actions/setup-java@v4
        with:
          distribution: temurin
          java-version: |
            8
            17
          cache: gradle

      - name: Set up Gradle
        uses: gradle/actions/setup-gradle@v4

      - name: Run lints
        run: ./scripts/lint
  test:
    timeout-minutes: 10
    name: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Set up Java
        uses: actions/setup-java@v4
        with:
          distribution: temurin
          java-version: |
            8
            17
          cache: gradle

      - name: Set up Gradle
        uses: gradle/gradle-build-action@v2

      - name: Run tests
        run: ./scripts/test
