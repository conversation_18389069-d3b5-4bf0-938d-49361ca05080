# This workflow is triggered when a GitHub release is created.
# It can also be run manually to re-publish to Sonatype in case it failed for some reason.
# You can run this workflow by navigating to https://www.github.com/braintrustdata/braintrust-java/actions/workflows/publish-sonatype.yml
name: Publish Sonatype
on:
  workflow_dispatch:

  release:
    types: [published]

jobs:
  publish:
    name: publish
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Set up Java
        uses: actions/setup-java@v3
        with:
          distribution: temurin
          java-version: |
            8
            17
          cache: gradle

      - name: Set up Gradle
        uses: gradle/gradle-build-action@v2

      - name: Publish to Sonatype
        run: |-
          export -- GPG_SIGNING_KEY_ID
          printenv -- GPG_SIGNING_KEY | gpg --batch --passphrase-fd 3 --import 3<<< "$GPG_SIGNING_PASSWORD"
          GPG_SIGNING_KEY_ID="$(gpg --with-colons --list-keys | awk -F : -- '/^pub:/ { getline; print "0x" substr($10, length($10) - 7) }')"
          ./gradlew publishAndReleaseToMavenCentral --stacktrace -PmavenCentralUsername="$SONATYPE_USERNAME" -PmavenCentralPassword="$SONATYPE_PASSWORD" --no-configuration-cache
        env:
          SONATYPE_USERNAME: ${{ secrets.BRAINTRUST_SONATYPE_USERNAME || secrets.SONATYPE_USERNAME }}
          SONATYPE_PASSWORD: ${{ secrets.BRAINTRUST_SONATYPE_PASSWORD || secrets.SONATYPE_PASSWORD }}
          GPG_SIGNING_KEY: ${{ secrets.BRAINTRUST_SONATYPE_GPG_SIGNING_KEY || secrets.GPG_SIGNING_KEY }}
          GPG_SIGNING_PASSWORD: ${{ secrets.BRAINTRUST_SONATYPE_GPG_SIGNING_PASSWORD || secrets.GPG_SIGNING_PASSWORD }}