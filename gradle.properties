org.gradle.caching=true
org.gradle.configuration-cache=true
org.gradle.parallel=true
org.gradle.daemon=false
# These options improve our compilation and test performance. They are inherited by the Kotlin daemon.
org.gradle.jvmargs=\
  -Xms1g \
  -Xmx4g \
  -XX:+UseParallelGC \
  -XX:InitialCodeCacheSize=256m \
  -XX:ReservedCodeCacheSize=1G \
  -XX:MetaspaceSize=256m \
  -XX:TieredStopAtLevel=1 \
  -XX:GCTimeRatio=4 \
  -XX:CICompilerCount=4 \
  -XX:+OptimizeStringConcat \
  -XX:+UseStringDeduplication
