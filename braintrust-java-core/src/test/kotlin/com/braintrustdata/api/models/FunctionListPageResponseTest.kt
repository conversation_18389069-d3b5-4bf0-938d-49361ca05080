// File generated from our OpenAPI spec by Stainless.

package com.braintrustdata.api.models

import com.braintrustdata.api.core.JsonValue
import com.braintrustdata.api.core.jsonMapper
import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import java.time.OffsetDateTime
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

internal class FunctionListPageResponseTest {

    @Test
    fun create() {
        val functionListPageResponse =
            FunctionListPageResponse.builder()
                .addObject(
                    Function.builder()
                        .id("182bd5e5-6e1a-4fe4-a799-aa6d9a6ab26e")
                        ._xactId("_xact_id")
                        .functionData(
                            Function.FunctionData.Prompt.builder()
                                .type(Function.FunctionData.Prompt.Type.PROMPT)
                                .build()
                        )
                        .logId(Function.LogId.P)
                        .name("name")
                        .orgId("182bd5e5-6e1a-4fe4-a799-aa6d9a6ab26e")
                        .projectId("182bd5e5-6e1a-4fe4-a799-aa6d9a6ab26e")
                        .slug("slug")
                        .created(OffsetDateTime.parse("2019-12-27T18:11:19.117Z"))
                        .description("description")
                        .functionSchema(
                            Function.FunctionSchema.builder()
                                .parameters(JsonValue.from(mapOf<String, Any>()))
                                .returns(JsonValue.from(mapOf<String, Any>()))
                                .build()
                        )
                        .functionType(Function.FunctionType.LLM)
                        .metadata(
                            Function.Metadata.builder()
                                .putAdditionalProperty("foo", JsonValue.from("bar"))
                                .build()
                        )
                        .origin(
                            Function.Origin.builder()
                                .objectId("182bd5e5-6e1a-4fe4-a799-aa6d9a6ab26e")
                                .objectType(AclObjectType.ORGANIZATION)
                                .internal_(true)
                                .build()
                        )
                        .promptData(
                            PromptData.builder()
                                .options(
                                    PromptOptions.builder()
                                        .model("model")
                                        .params(
                                            PromptOptions.Params.OpenAIModelParams.builder()
                                                .frequencyPenalty(0.0)
                                                .functionCall(
                                                    PromptOptions.Params.OpenAIModelParams
                                                        .FunctionCall
                                                        .UnionMember0
                                                        .AUTO
                                                )
                                                .maxCompletionTokens(0.0)
                                                .maxTokens(0.0)
                                                .n(0.0)
                                                .presencePenalty(0.0)
                                                .reasoningEffort(
                                                    PromptOptions.Params.OpenAIModelParams
                                                        .ReasoningEffort
                                                        .LOW
                                                )
                                                .responseFormat(
                                                    PromptOptions.Params.OpenAIModelParams
                                                        .ResponseFormat
                                                        .JsonObject
                                                        .builder()
                                                        .type(
                                                            PromptOptions.Params.OpenAIModelParams
                                                                .ResponseFormat
                                                                .JsonObject
                                                                .Type
                                                                .JSON_OBJECT
                                                        )
                                                        .build()
                                                )
                                                .addStop("string")
                                                .temperature(0.0)
                                                .toolChoice(
                                                    PromptOptions.Params.OpenAIModelParams
                                                        .ToolChoice
                                                        .UnionMember0
                                                        .AUTO
                                                )
                                                .topP(0.0)
                                                .useCache(true)
                                                .build()
                                        )
                                        .position("position")
                                        .build()
                                )
                                .origin(
                                    PromptData.Origin.builder()
                                        .projectId("project_id")
                                        .promptId("prompt_id")
                                        .promptVersion("prompt_version")
                                        .build()
                                )
                                .parser(
                                    PromptData.Parser.builder()
                                        .choiceScores(
                                            PromptData.Parser.ChoiceScores.builder()
                                                .putAdditionalProperty("foo", JsonValue.from(0))
                                                .build()
                                        )
                                        .type(PromptData.Parser.Type.LLM_CLASSIFIER)
                                        .useCot(true)
                                        .build()
                                )
                                .prompt(
                                    PromptData.Prompt.Completion.builder()
                                        .content("content")
                                        .type(PromptData.Prompt.Completion.Type.COMPLETION)
                                        .build()
                                )
                                .addToolFunction(
                                    PromptData.ToolFunction.Function.builder()
                                        .id("id")
                                        .type(PromptData.ToolFunction.Function.Type.FUNCTION)
                                        .build()
                                )
                                .build()
                        )
                        .addTag("string")
                        .build()
                )
                .build()

        assertThat(functionListPageResponse.objects())
            .containsExactly(
                Function.builder()
                    .id("182bd5e5-6e1a-4fe4-a799-aa6d9a6ab26e")
                    ._xactId("_xact_id")
                    .functionData(
                        Function.FunctionData.Prompt.builder()
                            .type(Function.FunctionData.Prompt.Type.PROMPT)
                            .build()
                    )
                    .logId(Function.LogId.P)
                    .name("name")
                    .orgId("182bd5e5-6e1a-4fe4-a799-aa6d9a6ab26e")
                    .projectId("182bd5e5-6e1a-4fe4-a799-aa6d9a6ab26e")
                    .slug("slug")
                    .created(OffsetDateTime.parse("2019-12-27T18:11:19.117Z"))
                    .description("description")
                    .functionSchema(
                        Function.FunctionSchema.builder()
                            .parameters(JsonValue.from(mapOf<String, Any>()))
                            .returns(JsonValue.from(mapOf<String, Any>()))
                            .build()
                    )
                    .functionType(Function.FunctionType.LLM)
                    .metadata(
                        Function.Metadata.builder()
                            .putAdditionalProperty("foo", JsonValue.from("bar"))
                            .build()
                    )
                    .origin(
                        Function.Origin.builder()
                            .objectId("182bd5e5-6e1a-4fe4-a799-aa6d9a6ab26e")
                            .objectType(AclObjectType.ORGANIZATION)
                            .internal_(true)
                            .build()
                    )
                    .promptData(
                        PromptData.builder()
                            .options(
                                PromptOptions.builder()
                                    .model("model")
                                    .params(
                                        PromptOptions.Params.OpenAIModelParams.builder()
                                            .frequencyPenalty(0.0)
                                            .functionCall(
                                                PromptOptions.Params.OpenAIModelParams.FunctionCall
                                                    .UnionMember0
                                                    .AUTO
                                            )
                                            .maxCompletionTokens(0.0)
                                            .maxTokens(0.0)
                                            .n(0.0)
                                            .presencePenalty(0.0)
                                            .reasoningEffort(
                                                PromptOptions.Params.OpenAIModelParams
                                                    .ReasoningEffort
                                                    .LOW
                                            )
                                            .responseFormat(
                                                PromptOptions.Params.OpenAIModelParams
                                                    .ResponseFormat
                                                    .JsonObject
                                                    .builder()
                                                    .type(
                                                        PromptOptions.Params.OpenAIModelParams
                                                            .ResponseFormat
                                                            .JsonObject
                                                            .Type
                                                            .JSON_OBJECT
                                                    )
                                                    .build()
                                            )
                                            .addStop("string")
                                            .temperature(0.0)
                                            .toolChoice(
                                                PromptOptions.Params.OpenAIModelParams.ToolChoice
                                                    .UnionMember0
                                                    .AUTO
                                            )
                                            .topP(0.0)
                                            .useCache(true)
                                            .build()
                                    )
                                    .position("position")
                                    .build()
                            )
                            .origin(
                                PromptData.Origin.builder()
                                    .projectId("project_id")
                                    .promptId("prompt_id")
                                    .promptVersion("prompt_version")
                                    .build()
                            )
                            .parser(
                                PromptData.Parser.builder()
                                    .choiceScores(
                                        PromptData.Parser.ChoiceScores.builder()
                                            .putAdditionalProperty("foo", JsonValue.from(0))
                                            .build()
                                    )
                                    .type(PromptData.Parser.Type.LLM_CLASSIFIER)
                                    .useCot(true)
                                    .build()
                            )
                            .prompt(
                                PromptData.Prompt.Completion.builder()
                                    .content("content")
                                    .type(PromptData.Prompt.Completion.Type.COMPLETION)
                                    .build()
                            )
                            .addToolFunction(
                                PromptData.ToolFunction.Function.builder()
                                    .id("id")
                                    .type(PromptData.ToolFunction.Function.Type.FUNCTION)
                                    .build()
                            )
                            .build()
                    )
                    .addTag("string")
                    .build()
            )
    }

    @Test
    fun roundtrip() {
        val jsonMapper = jsonMapper()
        val functionListPageResponse =
            FunctionListPageResponse.builder()
                .addObject(
                    Function.builder()
                        .id("182bd5e5-6e1a-4fe4-a799-aa6d9a6ab26e")
                        ._xactId("_xact_id")
                        .functionData(
                            Function.FunctionData.Prompt.builder()
                                .type(Function.FunctionData.Prompt.Type.PROMPT)
                                .build()
                        )
                        .logId(Function.LogId.P)
                        .name("name")
                        .orgId("182bd5e5-6e1a-4fe4-a799-aa6d9a6ab26e")
                        .projectId("182bd5e5-6e1a-4fe4-a799-aa6d9a6ab26e")
                        .slug("slug")
                        .created(OffsetDateTime.parse("2019-12-27T18:11:19.117Z"))
                        .description("description")
                        .functionSchema(
                            Function.FunctionSchema.builder()
                                .parameters(JsonValue.from(mapOf<String, Any>()))
                                .returns(JsonValue.from(mapOf<String, Any>()))
                                .build()
                        )
                        .functionType(Function.FunctionType.LLM)
                        .metadata(
                            Function.Metadata.builder()
                                .putAdditionalProperty("foo", JsonValue.from("bar"))
                                .build()
                        )
                        .origin(
                            Function.Origin.builder()
                                .objectId("182bd5e5-6e1a-4fe4-a799-aa6d9a6ab26e")
                                .objectType(AclObjectType.ORGANIZATION)
                                .internal_(true)
                                .build()
                        )
                        .promptData(
                            PromptData.builder()
                                .options(
                                    PromptOptions.builder()
                                        .model("model")
                                        .params(
                                            PromptOptions.Params.OpenAIModelParams.builder()
                                                .frequencyPenalty(0.0)
                                                .functionCall(
                                                    PromptOptions.Params.OpenAIModelParams
                                                        .FunctionCall
                                                        .UnionMember0
                                                        .AUTO
                                                )
                                                .maxCompletionTokens(0.0)
                                                .maxTokens(0.0)
                                                .n(0.0)
                                                .presencePenalty(0.0)
                                                .reasoningEffort(
                                                    PromptOptions.Params.OpenAIModelParams
                                                        .ReasoningEffort
                                                        .LOW
                                                )
                                                .responseFormat(
                                                    PromptOptions.Params.OpenAIModelParams
                                                        .ResponseFormat
                                                        .JsonObject
                                                        .builder()
                                                        .type(
                                                            PromptOptions.Params.OpenAIModelParams
                                                                .ResponseFormat
                                                                .JsonObject
                                                                .Type
                                                                .JSON_OBJECT
                                                        )
                                                        .build()
                                                )
                                                .addStop("string")
                                                .temperature(0.0)
                                                .toolChoice(
                                                    PromptOptions.Params.OpenAIModelParams
                                                        .ToolChoice
                                                        .UnionMember0
                                                        .AUTO
                                                )
                                                .topP(0.0)
                                                .useCache(true)
                                                .build()
                                        )
                                        .position("position")
                                        .build()
                                )
                                .origin(
                                    PromptData.Origin.builder()
                                        .projectId("project_id")
                                        .promptId("prompt_id")
                                        .promptVersion("prompt_version")
                                        .build()
                                )
                                .parser(
                                    PromptData.Parser.builder()
                                        .choiceScores(
                                            PromptData.Parser.ChoiceScores.builder()
                                                .putAdditionalProperty("foo", JsonValue.from(0))
                                                .build()
                                        )
                                        .type(PromptData.Parser.Type.LLM_CLASSIFIER)
                                        .useCot(true)
                                        .build()
                                )
                                .prompt(
                                    PromptData.Prompt.Completion.builder()
                                        .content("content")
                                        .type(PromptData.Prompt.Completion.Type.COMPLETION)
                                        .build()
                                )
                                .addToolFunction(
                                    PromptData.ToolFunction.Function.builder()
                                        .id("id")
                                        .type(PromptData.ToolFunction.Function.Type.FUNCTION)
                                        .build()
                                )
                                .build()
                        )
                        .addTag("string")
                        .build()
                )
                .build()

        val roundtrippedFunctionListPageResponse =
            jsonMapper.readValue(
                jsonMapper.writeValueAsString(functionListPageResponse),
                jacksonTypeRef<FunctionListPageResponse>(),
            )

        assertThat(roundtrippedFunctionListPageResponse).isEqualTo(functionListPageResponse)
    }
}
