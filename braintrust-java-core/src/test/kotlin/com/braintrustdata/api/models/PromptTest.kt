// File generated from our OpenAPI spec by Stainless.

package com.braintrustdata.api.models

import com.braintrustdata.api.core.JsonValue
import com.braintrustdata.api.core.jsonMapper
import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import java.time.OffsetDateTime
import kotlin.jvm.optionals.getOrNull
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

internal class PromptTest {

    @Test
    fun create() {
        val prompt =
            Prompt.builder()
                .id("182bd5e5-6e1a-4fe4-a799-aa6d9a6ab26e")
                ._xactId("_xact_id")
                .logId(Prompt.LogId.P)
                .name("name")
                .orgId("182bd5e5-6e1a-4fe4-a799-aa6d9a6ab26e")
                .projectId("182bd5e5-6e1a-4fe4-a799-aa6d9a6ab26e")
                .slug("slug")
                .created(OffsetDateTime.parse("2019-12-27T18:11:19.117Z"))
                .description("description")
                .functionType(Prompt.FunctionType.LLM)
                .metadata(
                    Prompt.Metadata.builder()
                        .putAdditionalProperty("foo", JsonValue.from("bar"))
                        .build()
                )
                .promptData(
                    PromptData.builder()
                        .options(
                            PromptOptions.builder()
                                .model("model")
                                .params(
                                    PromptOptions.Params.OpenAIModelParams.builder()
                                        .frequencyPenalty(0.0)
                                        .functionCall(
                                            PromptOptions.Params.OpenAIModelParams.FunctionCall
                                                .UnionMember0
                                                .AUTO
                                        )
                                        .maxCompletionTokens(0.0)
                                        .maxTokens(0.0)
                                        .n(0.0)
                                        .presencePenalty(0.0)
                                        .reasoningEffort(
                                            PromptOptions.Params.OpenAIModelParams.ReasoningEffort
                                                .LOW
                                        )
                                        .responseFormat(
                                            PromptOptions.Params.OpenAIModelParams.ResponseFormat
                                                .JsonObject
                                                .builder()
                                                .type(
                                                    PromptOptions.Params.OpenAIModelParams
                                                        .ResponseFormat
                                                        .JsonObject
                                                        .Type
                                                        .JSON_OBJECT
                                                )
                                                .build()
                                        )
                                        .addStop("string")
                                        .temperature(0.0)
                                        .toolChoice(
                                            PromptOptions.Params.OpenAIModelParams.ToolChoice
                                                .UnionMember0
                                                .AUTO
                                        )
                                        .topP(0.0)
                                        .useCache(true)
                                        .build()
                                )
                                .position("position")
                                .build()
                        )
                        .origin(
                            PromptData.Origin.builder()
                                .projectId("project_id")
                                .promptId("prompt_id")
                                .promptVersion("prompt_version")
                                .build()
                        )
                        .parser(
                            PromptData.Parser.builder()
                                .choiceScores(
                                    PromptData.Parser.ChoiceScores.builder()
                                        .putAdditionalProperty("foo", JsonValue.from(0))
                                        .build()
                                )
                                .type(PromptData.Parser.Type.LLM_CLASSIFIER)
                                .useCot(true)
                                .build()
                        )
                        .prompt(
                            PromptData.Prompt.Completion.builder()
                                .content("content")
                                .type(PromptData.Prompt.Completion.Type.COMPLETION)
                                .build()
                        )
                        .addToolFunction(
                            PromptData.ToolFunction.Function.builder()
                                .id("id")
                                .type(PromptData.ToolFunction.Function.Type.FUNCTION)
                                .build()
                        )
                        .build()
                )
                .addTag("string")
                .build()

        assertThat(prompt.id()).isEqualTo("182bd5e5-6e1a-4fe4-a799-aa6d9a6ab26e")
        assertThat(prompt._xactId()).isEqualTo("_xact_id")
        assertThat(prompt.logId()).isEqualTo(Prompt.LogId.P)
        assertThat(prompt.name()).isEqualTo("name")
        assertThat(prompt.orgId()).isEqualTo("182bd5e5-6e1a-4fe4-a799-aa6d9a6ab26e")
        assertThat(prompt.projectId()).isEqualTo("182bd5e5-6e1a-4fe4-a799-aa6d9a6ab26e")
        assertThat(prompt.slug()).isEqualTo("slug")
        assertThat(prompt.created()).contains(OffsetDateTime.parse("2019-12-27T18:11:19.117Z"))
        assertThat(prompt.description()).contains("description")
        assertThat(prompt.functionType()).contains(Prompt.FunctionType.LLM)
        assertThat(prompt.metadata())
            .contains(
                Prompt.Metadata.builder()
                    .putAdditionalProperty("foo", JsonValue.from("bar"))
                    .build()
            )
        assertThat(prompt.promptData())
            .contains(
                PromptData.builder()
                    .options(
                        PromptOptions.builder()
                            .model("model")
                            .params(
                                PromptOptions.Params.OpenAIModelParams.builder()
                                    .frequencyPenalty(0.0)
                                    .functionCall(
                                        PromptOptions.Params.OpenAIModelParams.FunctionCall
                                            .UnionMember0
                                            .AUTO
                                    )
                                    .maxCompletionTokens(0.0)
                                    .maxTokens(0.0)
                                    .n(0.0)
                                    .presencePenalty(0.0)
                                    .reasoningEffort(
                                        PromptOptions.Params.OpenAIModelParams.ReasoningEffort.LOW
                                    )
                                    .responseFormat(
                                        PromptOptions.Params.OpenAIModelParams.ResponseFormat
                                            .JsonObject
                                            .builder()
                                            .type(
                                                PromptOptions.Params.OpenAIModelParams
                                                    .ResponseFormat
                                                    .JsonObject
                                                    .Type
                                                    .JSON_OBJECT
                                            )
                                            .build()
                                    )
                                    .addStop("string")
                                    .temperature(0.0)
                                    .toolChoice(
                                        PromptOptions.Params.OpenAIModelParams.ToolChoice
                                            .UnionMember0
                                            .AUTO
                                    )
                                    .topP(0.0)
                                    .useCache(true)
                                    .build()
                            )
                            .position("position")
                            .build()
                    )
                    .origin(
                        PromptData.Origin.builder()
                            .projectId("project_id")
                            .promptId("prompt_id")
                            .promptVersion("prompt_version")
                            .build()
                    )
                    .parser(
                        PromptData.Parser.builder()
                            .choiceScores(
                                PromptData.Parser.ChoiceScores.builder()
                                    .putAdditionalProperty("foo", JsonValue.from(0))
                                    .build()
                            )
                            .type(PromptData.Parser.Type.LLM_CLASSIFIER)
                            .useCot(true)
                            .build()
                    )
                    .prompt(
                        PromptData.Prompt.Completion.builder()
                            .content("content")
                            .type(PromptData.Prompt.Completion.Type.COMPLETION)
                            .build()
                    )
                    .addToolFunction(
                        PromptData.ToolFunction.Function.builder()
                            .id("id")
                            .type(PromptData.ToolFunction.Function.Type.FUNCTION)
                            .build()
                    )
                    .build()
            )
        assertThat(prompt.tags().getOrNull()).containsExactly("string")
    }

    @Test
    fun roundtrip() {
        val jsonMapper = jsonMapper()
        val prompt =
            Prompt.builder()
                .id("182bd5e5-6e1a-4fe4-a799-aa6d9a6ab26e")
                ._xactId("_xact_id")
                .logId(Prompt.LogId.P)
                .name("name")
                .orgId("182bd5e5-6e1a-4fe4-a799-aa6d9a6ab26e")
                .projectId("182bd5e5-6e1a-4fe4-a799-aa6d9a6ab26e")
                .slug("slug")
                .created(OffsetDateTime.parse("2019-12-27T18:11:19.117Z"))
                .description("description")
                .functionType(Prompt.FunctionType.LLM)
                .metadata(
                    Prompt.Metadata.builder()
                        .putAdditionalProperty("foo", JsonValue.from("bar"))
                        .build()
                )
                .promptData(
                    PromptData.builder()
                        .options(
                            PromptOptions.builder()
                                .model("model")
                                .params(
                                    PromptOptions.Params.OpenAIModelParams.builder()
                                        .frequencyPenalty(0.0)
                                        .functionCall(
                                            PromptOptions.Params.OpenAIModelParams.FunctionCall
                                                .UnionMember0
                                                .AUTO
                                        )
                                        .maxCompletionTokens(0.0)
                                        .maxTokens(0.0)
                                        .n(0.0)
                                        .presencePenalty(0.0)
                                        .reasoningEffort(
                                            PromptOptions.Params.OpenAIModelParams.ReasoningEffort
                                                .LOW
                                        )
                                        .responseFormat(
                                            PromptOptions.Params.OpenAIModelParams.ResponseFormat
                                                .JsonObject
                                                .builder()
                                                .type(
                                                    PromptOptions.Params.OpenAIModelParams
                                                        .ResponseFormat
                                                        .JsonObject
                                                        .Type
                                                        .JSON_OBJECT
                                                )
                                                .build()
                                        )
                                        .addStop("string")
                                        .temperature(0.0)
                                        .toolChoice(
                                            PromptOptions.Params.OpenAIModelParams.ToolChoice
                                                .UnionMember0
                                                .AUTO
                                        )
                                        .topP(0.0)
                                        .useCache(true)
                                        .build()
                                )
                                .position("position")
                                .build()
                        )
                        .origin(
                            PromptData.Origin.builder()
                                .projectId("project_id")
                                .promptId("prompt_id")
                                .promptVersion("prompt_version")
                                .build()
                        )
                        .parser(
                            PromptData.Parser.builder()
                                .choiceScores(
                                    PromptData.Parser.ChoiceScores.builder()
                                        .putAdditionalProperty("foo", JsonValue.from(0))
                                        .build()
                                )
                                .type(PromptData.Parser.Type.LLM_CLASSIFIER)
                                .useCot(true)
                                .build()
                        )
                        .prompt(
                            PromptData.Prompt.Completion.builder()
                                .content("content")
                                .type(PromptData.Prompt.Completion.Type.COMPLETION)
                                .build()
                        )
                        .addToolFunction(
                            PromptData.ToolFunction.Function.builder()
                                .id("id")
                                .type(PromptData.ToolFunction.Function.Type.FUNCTION)
                                .build()
                        )
                        .build()
                )
                .addTag("string")
                .build()

        val roundtrippedPrompt =
            jsonMapper.readValue(jsonMapper.writeValueAsString(prompt), jacksonTypeRef<Prompt>())

        assertThat(roundtrippedPrompt).isEqualTo(prompt)
    }
}
