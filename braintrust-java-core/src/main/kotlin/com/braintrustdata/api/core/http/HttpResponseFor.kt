package com.braintrustdata.api.core.http

import java.io.InputStream

interface HttpResponseFor<T> : HttpResponse {

    fun parse(): T
}

@JvmSynthetic
internal fun <T> HttpResponse.parseable(parse: () -> T): HttpResponseFor<T> =
    object : HttpResponseFor<T> {

        private val parsed: T by lazy { parse() }

        override fun parse(): T = parsed

        override fun statusCode(): Int = <EMAIL>()

        override fun headers(): Headers = <EMAIL>()

        override fun body(): InputStream = <EMAIL>()

        override fun close() = <EMAIL>()
    }
