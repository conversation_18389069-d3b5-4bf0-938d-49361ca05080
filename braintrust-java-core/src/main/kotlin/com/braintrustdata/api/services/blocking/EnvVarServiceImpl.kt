// File generated from our OpenAPI spec by Stainless.

package com.braintrustdata.api.services.blocking

import com.braintrustdata.api.core.ClientOptions
import com.braintrustdata.api.core.JsonValue
import com.braintrustdata.api.core.RequestOptions
import com.braintrustdata.api.core.handlers.errorHandler
import com.braintrustdata.api.core.handlers.jsonHandler
import com.braintrustdata.api.core.handlers.withErrorHandler
import com.braintrustdata.api.core.http.HttpMethod
import com.braintrustdata.api.core.http.HttpRequest
import com.braintrustdata.api.core.http.HttpResponse.Handler
import com.braintrustdata.api.core.http.HttpResponseFor
import com.braintrustdata.api.core.http.json
import com.braintrustdata.api.core.http.parseable
import com.braintrustdata.api.core.prepare
import com.braintrustdata.api.models.EnvVar
import com.braintrustdata.api.models.EnvVarCreateParams
import com.braintrustdata.api.models.EnvVarDeleteParams
import com.braintrustdata.api.models.EnvVarListParams
import com.braintrustdata.api.models.EnvVarListResponse
import com.braintrustdata.api.models.EnvVarReplaceParams
import com.braintrustdata.api.models.EnvVarRetrieveParams
import com.braintrustdata.api.models.EnvVarUpdateParams

class EnvVarServiceImpl internal constructor(private val clientOptions: ClientOptions) :
    EnvVarService {

    private val withRawResponse: EnvVarService.WithRawResponse by lazy {
        WithRawResponseImpl(clientOptions)
    }

    override fun withRawResponse(): EnvVarService.WithRawResponse = withRawResponse

    override fun create(params: EnvVarCreateParams, requestOptions: RequestOptions): EnvVar =
        // post /v1/env_var
        withRawResponse().create(params, requestOptions).parse()

    override fun retrieve(params: EnvVarRetrieveParams, requestOptions: RequestOptions): EnvVar =
        // get /v1/env_var/{env_var_id}
        withRawResponse().retrieve(params, requestOptions).parse()

    override fun update(params: EnvVarUpdateParams, requestOptions: RequestOptions): EnvVar =
        // patch /v1/env_var/{env_var_id}
        withRawResponse().update(params, requestOptions).parse()

    override fun list(
        params: EnvVarListParams,
        requestOptions: RequestOptions,
    ): EnvVarListResponse =
        // get /v1/env_var
        withRawResponse().list(params, requestOptions).parse()

    override fun delete(params: EnvVarDeleteParams, requestOptions: RequestOptions): EnvVar =
        // delete /v1/env_var/{env_var_id}
        withRawResponse().delete(params, requestOptions).parse()

    override fun replace(params: EnvVarReplaceParams, requestOptions: RequestOptions): EnvVar =
        // put /v1/env_var
        withRawResponse().replace(params, requestOptions).parse()

    class WithRawResponseImpl internal constructor(private val clientOptions: ClientOptions) :
        EnvVarService.WithRawResponse {

        private val errorHandler: Handler<JsonValue> = errorHandler(clientOptions.jsonMapper)

        private val createHandler: Handler<EnvVar> =
            jsonHandler<EnvVar>(clientOptions.jsonMapper).withErrorHandler(errorHandler)

        override fun create(
            params: EnvVarCreateParams,
            requestOptions: RequestOptions,
        ): HttpResponseFor<EnvVar> {
            val request =
                HttpRequest.builder()
                    .method(HttpMethod.POST)
                    .addPathSegments("v1", "env_var")
                    .body(json(clientOptions.jsonMapper, params._body()))
                    .build()
                    .prepare(clientOptions, params)
            val requestOptions = requestOptions.applyDefaults(RequestOptions.from(clientOptions))
            val response = clientOptions.httpClient.execute(request, requestOptions)
            return response.parseable {
                response
                    .use { createHandler.handle(it) }
                    .also {
                        if (requestOptions.responseValidation!!) {
                            it.validate()
                        }
                    }
            }
        }

        private val retrieveHandler: Handler<EnvVar> =
            jsonHandler<EnvVar>(clientOptions.jsonMapper).withErrorHandler(errorHandler)

        override fun retrieve(
            params: EnvVarRetrieveParams,
            requestOptions: RequestOptions,
        ): HttpResponseFor<EnvVar> {
            val request =
                HttpRequest.builder()
                    .method(HttpMethod.GET)
                    .addPathSegments("v1", "env_var", params._pathParam(0))
                    .build()
                    .prepare(clientOptions, params)
            val requestOptions = requestOptions.applyDefaults(RequestOptions.from(clientOptions))
            val response = clientOptions.httpClient.execute(request, requestOptions)
            return response.parseable {
                response
                    .use { retrieveHandler.handle(it) }
                    .also {
                        if (requestOptions.responseValidation!!) {
                            it.validate()
                        }
                    }
            }
        }

        private val updateHandler: Handler<EnvVar> =
            jsonHandler<EnvVar>(clientOptions.jsonMapper).withErrorHandler(errorHandler)

        override fun update(
            params: EnvVarUpdateParams,
            requestOptions: RequestOptions,
        ): HttpResponseFor<EnvVar> {
            val request =
                HttpRequest.builder()
                    .method(HttpMethod.PATCH)
                    .addPathSegments("v1", "env_var", params._pathParam(0))
                    .body(json(clientOptions.jsonMapper, params._body()))
                    .build()
                    .prepare(clientOptions, params)
            val requestOptions = requestOptions.applyDefaults(RequestOptions.from(clientOptions))
            val response = clientOptions.httpClient.execute(request, requestOptions)
            return response.parseable {
                response
                    .use { updateHandler.handle(it) }
                    .also {
                        if (requestOptions.responseValidation!!) {
                            it.validate()
                        }
                    }
            }
        }

        private val listHandler: Handler<EnvVarListResponse> =
            jsonHandler<EnvVarListResponse>(clientOptions.jsonMapper).withErrorHandler(errorHandler)

        override fun list(
            params: EnvVarListParams,
            requestOptions: RequestOptions,
        ): HttpResponseFor<EnvVarListResponse> {
            val request =
                HttpRequest.builder()
                    .method(HttpMethod.GET)
                    .addPathSegments("v1", "env_var")
                    .build()
                    .prepare(clientOptions, params)
            val requestOptions = requestOptions.applyDefaults(RequestOptions.from(clientOptions))
            val response = clientOptions.httpClient.execute(request, requestOptions)
            return response.parseable {
                response
                    .use { listHandler.handle(it) }
                    .also {
                        if (requestOptions.responseValidation!!) {
                            it.validate()
                        }
                    }
            }
        }

        private val deleteHandler: Handler<EnvVar> =
            jsonHandler<EnvVar>(clientOptions.jsonMapper).withErrorHandler(errorHandler)

        override fun delete(
            params: EnvVarDeleteParams,
            requestOptions: RequestOptions,
        ): HttpResponseFor<EnvVar> {
            val request =
                HttpRequest.builder()
                    .method(HttpMethod.DELETE)
                    .addPathSegments("v1", "env_var", params._pathParam(0))
                    .apply { params._body().ifPresent { body(json(clientOptions.jsonMapper, it)) } }
                    .build()
                    .prepare(clientOptions, params)
            val requestOptions = requestOptions.applyDefaults(RequestOptions.from(clientOptions))
            val response = clientOptions.httpClient.execute(request, requestOptions)
            return response.parseable {
                response
                    .use { deleteHandler.handle(it) }
                    .also {
                        if (requestOptions.responseValidation!!) {
                            it.validate()
                        }
                    }
            }
        }

        private val replaceHandler: Handler<EnvVar> =
            jsonHandler<EnvVar>(clientOptions.jsonMapper).withErrorHandler(errorHandler)

        override fun replace(
            params: EnvVarReplaceParams,
            requestOptions: RequestOptions,
        ): HttpResponseFor<EnvVar> {
            val request =
                HttpRequest.builder()
                    .method(HttpMethod.PUT)
                    .addPathSegments("v1", "env_var")
                    .body(json(clientOptions.jsonMapper, params._body()))
                    .build()
                    .prepare(clientOptions, params)
            val requestOptions = requestOptions.applyDefaults(RequestOptions.from(clientOptions))
            val response = clientOptions.httpClient.execute(request, requestOptions)
            return response.parseable {
                response
                    .use { replaceHandler.handle(it) }
                    .also {
                        if (requestOptions.responseValidation!!) {
                            it.validate()
                        }
                    }
            }
        }
    }
}
